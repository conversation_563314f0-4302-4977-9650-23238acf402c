use crate::AuthError;
use chrono::{DateTime, Utc};
use serde_json::json;

/// Enhanced error handling for cross-client token validation scenarios
impl AuthError {
  /// Creates a TokenExpired error with the given expiry timestamp
  pub fn token_expired(expires_at: DateTime<Utc>) -> Self {
    Self::TokenExpired { expires_at }
  }

  /// Creates an InvalidIssuer error with the given issuer
  pub fn invalid_issuer(issuer: &str) -> Self {
    Self::InvalidIssuer(issuer.to_string())
  }

  /// Creates a TokenExchangeFailed error with the given reason
  pub fn token_exchange_failed(reason: &str) -> Self {
    Self::TokenExchangeFailed(reason.to_string())
  }

  /// Creates a CacheError with the given reason
  pub fn cache_error(reason: &str) -> Self {
    Self::CacheError(reason.to_string())
  }

  /// Converts TokenExpired error to JSON response format as specified in functional requirements
  pub fn to_token_expired_json(&self) -> Option<serde_json::Value> {
    match self {
      Self::TokenExpired { expires_at } => Some(json!({
        "error": "token_expired",
        "error_description": "The provided token has expired",
        "expires_at": expires_at.to_rfc3339()
      })),
      _ => None,
    }
  }

  /// Checks if this error should return HTTP 401 status
  pub fn is_unauthorized(&self) -> bool {
    matches!(
      self,
      Self::TokenExpired { .. }
        | Self::InvalidIssuer(_)
        | Self::TokenExchangeFailed(_)
        | Self::TokenInactive
        | Self::TokenNotFound
        | Self::InvalidToken(_)
        | Self::MissingRoles
        | Self::InvalidAccess
    )
  }
}

#[cfg(test)]
mod tests {
  use super::*;
  use chrono::{Duration, Utc};
  use objs::{test_utils::setup_l10n, ApiError, AppError, FluentLocalizationService};
  use rstest::rstest;
  use std::sync::Arc;

  #[rstest]
  fn test_token_expired_error_creation() {
    let expires_at = Utc::now() + Duration::hours(1);
    let error = AuthError::token_expired(expires_at);
    
    match error {
      AuthError::TokenExpired { expires_at: exp } => {
        assert_eq!(exp, expires_at);
      }
      _ => panic!("Expected TokenExpired error"),
    }
  }

  #[rstest]
  fn test_invalid_issuer_error_creation() {
    let issuer = "https://malicious.example.com";
    let error = AuthError::invalid_issuer(issuer);
    
    match error {
      AuthError::InvalidIssuer(iss) => {
        assert_eq!(iss, issuer);
      }
      _ => panic!("Expected InvalidIssuer error"),
    }
  }

  #[rstest]
  fn test_token_exchange_failed_error_creation() {
    let reason = "Keycloak server unreachable";
    let error = AuthError::token_exchange_failed(reason);
    
    match error {
      AuthError::TokenExchangeFailed(r) => {
        assert_eq!(r, reason);
      }
      _ => panic!("Expected TokenExchangeFailed error"),
    }
  }

  #[rstest]
  fn test_cache_error_creation() {
    let reason = "Cache service unavailable";
    let error = AuthError::cache_error(reason);
    
    match error {
      AuthError::CacheError(r) => {
        assert_eq!(r, reason);
      }
      _ => panic!("Expected CacheError error"),
    }
  }

  #[rstest]
  fn test_token_expired_json_format() {
    let expires_at = Utc::now() + Duration::hours(1);
    let error = AuthError::token_expired(expires_at);
    
    let json = error.to_token_expired_json().expect("Should return JSON");
    
    assert_eq!(json["error"], "token_expired");
    assert_eq!(json["error_description"], "The provided token has expired");
    assert_eq!(json["expires_at"], expires_at.to_rfc3339());
  }

  #[rstest]
  fn test_non_token_expired_json_returns_none() {
    let error = AuthError::invalid_issuer("test");
    assert!(error.to_token_expired_json().is_none());
  }

  #[rstest]
  #[case::token_expired(AuthError::token_expired(Utc::now()), true)]
  #[case::invalid_issuer(AuthError::invalid_issuer("test"), true)]
  #[case::token_exchange_failed(AuthError::token_exchange_failed("test"), true)]
  #[case::token_inactive(AuthError::TokenInactive, true)]
  #[case::token_not_found(AuthError::TokenNotFound, true)]
  #[case::invalid_token(AuthError::InvalidToken("test".to_string()), true)]
  #[case::missing_roles(AuthError::MissingRoles, true)]
  #[case::invalid_access(AuthError::InvalidAccess, true)]
  #[case::cache_error(AuthError::cache_error("test"), false)]
  fn test_is_unauthorized(#[case] error: AuthError, #[case] expected: bool) {
    assert_eq!(error.is_unauthorized(), expected);
  }

  #[rstest]
  fn test_token_expired_api_error_conversion(
    #[from(setup_l10n)] _setup_l10n: &Arc<FluentLocalizationService>,
  ) {
    let expires_at = Utc::now() + Duration::hours(1);
    let error = AuthError::token_expired(expires_at);
    
    let api_error = ApiError::from(error);
    
    assert_eq!(api_error.status, 401);
    assert_eq!(api_error.error_type, "Authentication");
    assert_eq!(api_error.code, "auth_error-token_expired");
    assert!(api_error.args.contains_key("expires_at"));
  }

  #[rstest]
  fn test_invalid_issuer_api_error_conversion(
    #[from(setup_l10n)] _setup_l10n: &Arc<FluentLocalizationService>,
  ) {
    let issuer = "https://malicious.example.com";
    let error = AuthError::invalid_issuer(issuer);
    
    let api_error = ApiError::from(error);
    
    assert_eq!(api_error.status, 401);
    assert_eq!(api_error.error_type, "Authentication");
    assert_eq!(api_error.code, "auth_error-invalid_issuer");
    assert_eq!(api_error.args.get("var_0"), Some(&issuer.to_string()));
  }

  #[rstest]
  fn test_token_exchange_failed_api_error_conversion(
    #[from(setup_l10n)] _setup_l10n: &Arc<FluentLocalizationService>,
  ) {
    let reason = "Keycloak server unreachable";
    let error = AuthError::token_exchange_failed(reason);
    
    let api_error = ApiError::from(error);
    
    assert_eq!(api_error.status, 401);
    assert_eq!(api_error.error_type, "Authentication");
    assert_eq!(api_error.code, "auth_error-token_exchange_failed");
    assert_eq!(api_error.args.get("var_0"), Some(&reason.to_string()));
  }

  #[rstest]
  fn test_cache_error_api_error_conversion(
    #[from(setup_l10n)] _setup_l10n: &Arc<FluentLocalizationService>,
  ) {
    let reason = "Cache service unavailable";
    let error = AuthError::cache_error(reason);
    
    let api_error = ApiError::from(error);
    
    assert_eq!(api_error.status, 500);
    assert_eq!(api_error.error_type, "InternalServer");
    assert_eq!(api_error.code, "auth_error-cache_error");
    assert_eq!(api_error.args.get("var_0"), Some(&reason.to_string()));
  }
}
