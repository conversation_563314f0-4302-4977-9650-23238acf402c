<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OAuth Cross-Client Token Exchange Test</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .info { background-color: #e7f3ff; color: #004085; }
        button { background-color: #007bff; color: white; padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; margin: 10px 0; }
        button:hover { background-color: #0056b3; }
        .status { margin: 15px 0; padding: 10px; border-radius: 4px; }
        .status.success { background-color: #d4edda; color: #155724; }
        .status.error { background-color: #f8d7da; color: #721c24; }
        .status.info { background-color: #e7f3ff; color: #004085; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 OAuth Cross-Client Token Exchange Test</h1>
        
        <div class="section info">
            <h3>Test Configuration</h3>
            <p><strong>Third-party client:</strong> test-client-059a5742-2d7c-44cd-b2fa-2952e8fabee3</p>
            <p><strong>Our app client:</strong> test-resource-059a5742-2d7c-44cd-b2fa-2952e8fabee3</p>
            <p><strong>Server URL:</strong> http://127.0.0.1:26452</p>
            <p><strong>Redirect URI:</strong> <span id="redirectUri"></span></p>
        </div>

        <div class="section">
            <h3>Step 1: Authenticate with Third-Party Client</h3>
            <p>This will start OAuth flow with the third-party client. The token will then be exchanged by our server.</p>
            <button id="loginBtn" onclick="startOAuthFlow()">Start OAuth Flow</button>
            <div id="status" class="status" style="display: none;"></div>
        </div>

        <div class="section">
            <h3>Current Status</h3>
            <div id="tokenStatus">No token stored</div>
            <button onclick="clearStorage()" style="background-color: #dc3545;">Clear Storage</button>
            <button onclick="testApiCall()" style="background-color: #28a745;">Test API Call</button>
        </div>
    </div>

    <script>
        // OAuth configuration
        const CONFIG = {
            authUrl: 'https://dev-id.getbodhi.app',
            realm: 'bodhi',
            clientId: 'test-client-059a5742-2d7c-44cd-b2fa-2952e8fabee3',
            redirectUri: window.location.origin + '/auth/callback/',
            scope: 'openid profile email offline_access'
        };

        // Update redirect URI display
        document.getElementById('redirectUri').textContent = CONFIG.redirectUri;

        // PKCE helper functions
        function generateRandomString(length) {
            const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~';
            let result = '';
            for (let i = 0; i < length; i++) {
                result += charset.charAt(Math.floor(Math.random() * charset.length));
            }
            return result;
        }

        async function generateCodeChallenge(codeVerifier) {
            const encoder = new TextEncoder();
            const data = encoder.encode(codeVerifier);
            const digest = await crypto.subtle.digest('SHA-256', data);
            return btoa(String.fromCharCode(...new Uint8Array(digest)))
                .replace(/\+/g, '-')
                .replace(/\//g, '_')
                .replace(/=/g, '');
        }

        async function startOAuthFlow() {
            try {
                showStatus('Preparing OAuth flow...', 'info');
                
                const codeVerifier = generateRandomString(128);
                const codeChallenge = await generateCodeChallenge(codeVerifier);
                const state = generateRandomString(32);

                localStorage.setItem('oauth_code_verifier', codeVerifier);
                localStorage.setItem('oauth_state', state);
                localStorage.setItem('oauth_timestamp', Date.now().toString());

                const authUrl = new URL(CONFIG.authUrl + '/realms/' + CONFIG.realm + '/protocol/openid-connect/auth');
                authUrl.searchParams.set('client_id', CONFIG.clientId);
                authUrl.searchParams.set('redirect_uri', CONFIG.redirectUri);
                authUrl.searchParams.set('response_type', 'code');
                authUrl.searchParams.set('scope', CONFIG.scope);
                authUrl.searchParams.set('state', state);
                authUrl.searchParams.set('code_challenge', codeChallenge);
                authUrl.searchParams.set('code_challenge_method', 'S256');

                console.log('Redirecting to:', authUrl.toString());
                showStatus('Redirecting to authentication server...', 'success');
                
                window.location.href = authUrl.toString();
                
            } catch (error) {
                console.error('OAuth flow error:', error);
                showStatus('Error starting OAuth flow: ' + error.message, 'error');
            }
        }

        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = 'status ' + type;
            statusDiv.style.display = 'block';
        }

        function clearStorage() {
            localStorage.clear();
            updateTokenStatus();
            showStatus('Storage cleared', 'success');
        }

        async function testApiCall() {
            const token = localStorage.getItem('access_token');
            if (!token) {
                showStatus('No token available for API test', 'error');
                return;
            }

            try {
                showStatus('Testing API call with cross-client token...', 'info');
                
                const response = await fetch('http://127.0.0.1:26452/ping', {
                    headers: {
                        'Authorization': 'Bearer ' + token
                    }
                });

                if (response.ok) {
                    showStatus('✅ API call successful! Cross-client token exchange worked!', 'success');
                } else {
                    showStatus('❌ API call failed: ' + response.status + ' ' + response.statusText, 'error');
                }
            } catch (error) {
                showStatus('❌ API call error: ' + error.message, 'error');
            }
        }

        function updateTokenStatus() {
            const token = localStorage.getItem('access_token');
            const statusDiv = document.getElementById('tokenStatus');
            
            if (token) {
                try {
                    const payload = JSON.parse(atob(token.split('.')[1]));
                    const exp = new Date(payload.exp * 1000);
                    const isExpired = Date.now() > payload.exp * 1000;
                    
                    statusDiv.innerHTML = 
                        '<strong>Token Status:</strong> ' + (isExpired ? 'EXPIRED' : 'VALID') + '<br>' +
                        '<strong>Expires:</strong> ' + exp.toLocaleString() + '<br>' +
                        '<strong>Client:</strong> ' + (payload.azp || 'Unknown') + '<br>' +
                        '<strong>Subject:</strong> ' + (payload.sub || 'Unknown');
                } catch (e) {
                    statusDiv.textContent = 'Invalid token stored';
                }
            } else {
                statusDiv.textContent = 'No token stored';
            }
        }

        document.addEventListener('DOMContentLoaded', updateTokenStatus);
    </script>
</body>
</html>