// Main application logic

document.addEventListener('DOMContentLoaded', function() {
    // Load configuration and update UI
    if (window.OAUTH_TEST_CONFIG) {
        const config = window.OAUTH_TEST_CONFIG;
        
        // Update configuration display
        document.getElementById('thirdPartyClientId').textContent = config.thirdPartyClientId;
        document.getElementById('appClientId').textContent = config.appClientId;
        document.getElementById('serverUrl').textContent = config.serverUrl;
        
        // Update redirect URI display
        const redirectUri = `http://test-client.localhost:${window.location.port}/auth/callback/`;
        document.getElementById('redirectUri').textContent = redirectUri;
        
        console.log('OAuth Test Configuration loaded:', config);
    } else {
        console.error('OAuth test configuration not found!');
        showStatus('Configuration not loaded', 'error');
    }
    
    // Update token status on page load
    updateTokenStatus();
    
    // Check if we're on the callback page
    if (window.location.pathname.includes('/auth/callback/')) {
        processCallback();
    }
});

// Handle OAuth callback
async function processCallback() {
    try {
        const urlParams = new URLSearchParams(window.location.search);
        const code = urlParams.get('code');
        const state = urlParams.get('state');
        const error = urlParams.get('error');

        if (error) {
            throw new Error(`OAuth error: ${error} - ${urlParams.get('error_description') || 'Unknown error'}`);
        }

        if (!code) {
            throw new Error('No authorization code received');
        }

        // Verify state parameter
        const storedState = localStorage.getItem('oauth_state');
        if (!storedState || storedState !== state) {
            throw new Error('Invalid state parameter - possible CSRF attack');
        }

        // Get stored PKCE parameters
        const codeVerifier = localStorage.getItem('oauth_code_verifier');
        if (!codeVerifier) {
            throw new Error('No code verifier found in storage');
        }

        showStatus('Exchanging authorization code for token...', 'info');

        // Exchange code for token
        const tokenResponse = await exchangeCodeForToken(code, codeVerifier);

        if (tokenResponse.access_token) {
            // Store the token
            localStorage.setItem('access_token', tokenResponse.access_token);
            if (tokenResponse.refresh_token) {
                localStorage.setItem('refresh_token', tokenResponse.refresh_token);
            }

            showStatus('✅ Token received successfully! This token will be exchanged by our server.', 'success');
            
            // Clean up OAuth state
            localStorage.removeItem('oauth_state');
            localStorage.removeItem('oauth_code_verifier');
            localStorage.removeItem('oauth_timestamp');

            // Redirect back to main page after a short delay
            setTimeout(() => {
                window.location.href = '/';
            }, 2000);

        } else {
            throw new Error('No access token in response');
        }

    } catch (error) {
        console.error('Callback processing error:', error);
        showStatus('❌ Error: ' + error.message, 'error');
    }
}
