<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OAuth Test - Home</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .auth-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 0;
        }
        button:hover {
            background-color: #0056b3;
        }
        .info {
            background-color: #e7f3ff;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        .status {
            margin: 15px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>OAuth 2.0 Cross-Client Token Exchange Test</h1>
        
        <div class="info">
            <h3>Test Scenario:</h3>
            <p>This test demonstrates OAuth 2.0 Token Exchange (RFC 8693) where:</p>
            <ul>
                <li>We authenticate with a <strong>third-party client</strong> (test-resource-*)</li>
                <li>The token is then <strong>exchanged</strong> for our app's client token</li>
                <li>We use the exchanged token to make API calls to our server</li>
            </ul>
        </div>

        <div class="auth-section">
            <h3>Step 1: Authenticate with Third-Party Client</h3>
            <p>Click below to start OAuth flow with the external client:</p>
            <button id="loginBtn" onclick="startOAuthFlow()">Login with Third-Party Client</button>
            
            <div id="status" class="status" style="display: none;"></div>
        </div>

        <div class="auth-section">
            <h3>Current Status:</h3>
            <div id="tokenStatus">No token stored</div>
            <button onclick="clearStorage()" style="background-color: #dc3545;">Clear Storage</button>
            <button onclick="checkToken()" style="background-color: #28a745;">Check Token</button>
        </div>
    </div>

    <script>
        // OAuth configuration - using third-party client for initial auth
        const CONFIG = {
            authUrl: 'https://dev-id.getbodhi.app',
            realm: 'bodhi',
            clientId: 'test-resource-059a5742-2d7c-44cd-b2fa-2952e8fabee3', // Third-party client
            redirectUri: window.location.origin + '/auth/callback/',
            scope: 'openid profile email offline_access'
        };

        // PKCE helper functions
        function generateRandomString(length) {
            const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~';
            let result = '';
            for (let i = 0; i < length; i++) {
                result += charset.charAt(Math.floor(Math.random() * charset.length));
            }
            return result;
        }

        async function generateCodeChallenge(codeVerifier) {
            const encoder = new TextEncoder();
            const data = encoder.encode(codeVerifier);
            const digest = await crypto.subtle.digest('SHA-256', data);
            return btoa(String.fromCharCode(...new Uint8Array(digest)))
                .replace(/\+/g, '-')
                .replace(/\//g, '_')
                .replace(/=/g, '');
        }

        async function startOAuthFlow() {
            try {
                showStatus('Preparing OAuth flow...', 'info');
                
                // Generate PKCE parameters
                const codeVerifier = generateRandomString(128);
                const codeChallenge = await generateCodeChallenge(codeVerifier);
                const state = generateRandomString(32);

                // Store PKCE parameters
                localStorage.setItem('oauth_code_verifier', codeVerifier);
                localStorage.setItem('oauth_state', state);
                localStorage.setItem('oauth_timestamp', Date.now().toString());

                // Build authorization URL
                const authUrl = new URL(`${CONFIG.authUrl}/realms/${CONFIG.realm}/protocol/openid-connect/auth`);
                authUrl.searchParams.set('client_id', CONFIG.clientId);
                authUrl.searchParams.set('redirect_uri', CONFIG.redirectUri);
                authUrl.searchParams.set('response_type', 'code');
                authUrl.searchParams.set('scope', CONFIG.scope);
                authUrl.searchParams.set('state', state);
                authUrl.searchParams.set('code_challenge', codeChallenge);
                authUrl.searchParams.set('code_challenge_method', 'S256');

                console.log('Redirecting to:', authUrl.toString());
                showStatus('Redirecting to authentication server...', 'success');
                
                // Redirect to auth server
                window.location.href = authUrl.toString();
                
            } catch (error) {
                console.error('OAuth flow error:', error);
                showStatus('Error starting OAuth flow: ' + error.message, 'error');
            }
        }

        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            statusDiv.style.display = 'block';
        }

        function clearStorage() {
            localStorage.clear();
            updateTokenStatus();
            showStatus('Storage cleared', 'success');
        }

        function checkToken() {
            const token = localStorage.getItem('access_token');
            if (token) {
                try {
                    const payload = JSON.parse(atob(token.split('.')[1]));
                    showStatus(`Token valid until: ${new Date(payload.exp * 1000).toLocaleString()}`, 'success');
                } catch (e) {
                    showStatus('Invalid token format', 'error');
                }
            } else {
                showStatus('No token found', 'error');
            }
        }

        function updateTokenStatus() {
            const token = localStorage.getItem('access_token');
            const statusDiv = document.getElementById('tokenStatus');
            
            if (token) {
                try {
                    const payload = JSON.parse(atob(token.split('.')[1]));
                    const exp = new Date(payload.exp * 1000);
                    const isExpired = Date.now() > payload.exp * 1000;
                    
                    statusDiv.innerHTML = `
                        <strong>Token Status:</strong> ${isExpired ? 'EXPIRED' : 'VALID'}<br>
                        <strong>Expires:</strong> ${exp.toLocaleString()}<br>
                        <strong>Client:</strong> ${payload.azp || 'Unknown'}<br>
                        <strong>Subject:</strong> ${payload.sub || 'Unknown'}
                    `;
                    
                    if (!isExpired) {
                        statusDiv.innerHTML += '<br><a href="chat.html" style="color: #007bff; text-decoration: none;">→ Go to Chat Test</a>';
                    }
                } catch (e) {
                    statusDiv.textContent = 'Invalid token stored';
                }
            } else {
                statusDiv.textContent = 'No token stored';
            }
        }

        // Update status on page load
        document.addEventListener('DOMContentLoaded', updateTokenStatus);
    </script>
</body>
</html>
