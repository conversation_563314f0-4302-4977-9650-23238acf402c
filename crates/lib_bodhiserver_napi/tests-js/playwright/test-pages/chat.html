<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat API Test - Cross-Client Token Exchange</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .token-info {
            background-color: #e7f3ff;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 12px;
            word-break: break-all;
        }
        .form-group {
            margin: 15px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px 10px 0;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .response {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .status.info {
            background-color: #e7f3ff;
            color: #004085;
        }
        .highlight {
            background-color: #fff3cd;
            padding: 2px 4px;
            border-radius: 2px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 Chat API Test - Cross-Client Token Exchange</h1>
        
        <div class="section">
            <h3>Token Information</h3>
            <div id="tokenInfo" class="token-info">Loading token information...</div>
            <button onclick="refreshTokenInfo()">Refresh Token Info</button>
            <button onclick="goHome()" style="background-color: #6c757d;">Back to Home</button>
        </div>

        <div class="section">
            <h3>🎯 Test Scenario</h3>
            <div class="status info">
                <strong>Cross-Client Token Exchange Test:</strong><br>
                1. We authenticated with a <span class="highlight">third-party client</span> (test-resource-*)<br>
                2. Our server will <span class="highlight">exchange this token</span> for our app's client token<br>
                3. The exchanged token will be used to make API calls<br>
                4. This tests the OAuth 2.0 Token Exchange (RFC 8693) implementation
            </div>
        </div>

        <div class="section">
            <h3>Chat Configuration</h3>
            <div class="form-group">
                <label for="serverPort">LLM Server Port:</label>
                <input type="number" id="serverPort" value="11434" placeholder="Enter LLM server port">
            </div>
            <div class="form-group">
                <label for="modelName">Model Name:</label>
                <input type="text" id="modelName" value="llama3.2:1b" placeholder="Enter model name">
            </div>
        </div>

        <div class="section">
            <h3>Chat Test</h3>
            <div class="form-group">
                <label for="chatMessage">Message:</label>
                <textarea id="chatMessage" rows="3" placeholder="Enter your message...">What day comes after Monday?</textarea>
            </div>
            <button id="sendBtn" onclick="sendChatMessage()">Send Message</button>
            <button onclick="clearResponse()">Clear Response</button>
            
            <div id="status" class="status" style="display: none;"></div>
            <div id="response" class="response" style="display: none;"></div>
        </div>

        <div class="section">
            <h3>Test Validation</h3>
            <div id="testResults" class="status" style="display: none;"></div>
            <button onclick="validateResponse()">Validate Response</button>
        </div>
    </div>

    <script>
        let lastResponse = null;

        function refreshTokenInfo() {
            const token = localStorage.getItem('access_token');
            const tokenInfoDiv = document.getElementById('tokenInfo');
            
            if (!token) {
                tokenInfoDiv.innerHTML = '<span style="color: red;">❌ No token found. Please go back and authenticate first.</span>';
                return;
            }

            try {
                const payload = JSON.parse(atob(token.split('.')[1]));
                const exp = new Date(payload.exp * 1000);
                const isExpired = Date.now() > payload.exp * 1000;
                
                tokenInfoDiv.innerHTML = `
                    <strong>Token Status:</strong> ${isExpired ? '❌ EXPIRED' : '✅ VALID'}<br>
                    <strong>Expires:</strong> ${exp.toLocaleString()}<br>
                    <strong>Issued By Client:</strong> ${payload.azp || 'Unknown'}<br>
                    <strong>Subject:</strong> ${payload.sub || 'Unknown'}<br>
                    <strong>Issuer:</strong> ${payload.iss || 'Unknown'}<br>
                    <strong>Scopes:</strong> ${payload.scope || 'Unknown'}<br>
                    <strong>Token Type:</strong> ${payload.typ || 'Unknown'}<br>
                    <br>
                    <strong>🔄 Cross-Client Exchange:</strong> This token from client "${payload.azp}" will be exchanged by our server for our app's client token.
                `;
                
                if (isExpired) {
                    tokenInfoDiv.innerHTML += '<br><span style="color: red;">⚠️ Token is expired. Please re-authenticate.</span>';
                }
            } catch (e) {
                tokenInfoDiv.innerHTML = '<span style="color: red;">❌ Invalid token format</span>';
            }
        }

        async function sendChatMessage() {
            const token = localStorage.getItem('access_token');
            if (!token) {
                showStatus('❌ No token found. Please authenticate first.', 'error');
                return;
            }

            const serverPort = document.getElementById('serverPort').value;
            const modelName = document.getElementById('modelName').value;
            const message = document.getElementById('chatMessage').value;

            if (!message.trim()) {
                showStatus('❌ Please enter a message', 'error');
                return;
            }

            const sendBtn = document.getElementById('sendBtn');
            sendBtn.disabled = true;
            sendBtn.textContent = 'Sending...';

            try {
                showStatus('🔄 Sending request with cross-client token...', 'info');
                
                const requestBody = {
                    model: modelName,
                    messages: [
                        {
                            role: "user",
                            content: message
                        }
                    ],
                    max_tokens: 100,
                    temperature: 0.7
                };

                logResponse('Request Details:', {
                    url: `http://localhost:${serverPort}/v1/chat/completions`,
                    method: 'POST',
                    headers: {
                        'Authorization': 'Bearer [TOKEN_FROM_THIRD_PARTY_CLIENT]',
                        'Content-Type': 'application/json'
                    },
                    body: requestBody
                });

                const response = await fetch(`http://localhost:${serverPort}/v1/chat/completions`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                });

                const responseText = await response.text();
                
                logResponse('Response Details:', {
                    status: response.status,
                    statusText: response.statusText,
                    headers: Object.fromEntries(response.headers.entries()),
                    body: responseText
                });

                if (response.ok) {
                    const data = JSON.parse(responseText);
                    lastResponse = data;
                    
                    if (data.choices && data.choices[0] && data.choices[0].message) {
                        const assistantMessage = data.choices[0].message.content;
                        showStatus('✅ Success! Cross-client token exchange worked!', 'success');
                        logResponse('Assistant Response:', assistantMessage);
                    } else {
                        showStatus('⚠️ Unexpected response format', 'error');
                    }
                } else {
                    showStatus(`❌ Request failed: ${response.status} ${response.statusText}`, 'error');
                    
                    // Try to parse error response
                    try {
                        const errorData = JSON.parse(responseText);
                        logResponse('Error Details:', errorData);
                    } catch (e) {
                        logResponse('Raw Error Response:', responseText);
                    }
                }

            } catch (error) {
                console.error('Chat request error:', error);
                showStatus('❌ Network error: ' + error.message, 'error');
                logResponse('Network Error:', error.toString());
            } finally {
                sendBtn.disabled = false;
                sendBtn.textContent = 'Send Message';
            }
        }

        function validateResponse() {
            if (!lastResponse) {
                showStatus('❌ No response to validate. Send a message first.', 'error');
                return;
            }

            const testResultsDiv = document.getElementById('testResults');
            const message = document.getElementById('chatMessage').value.toLowerCase();
            
            let results = [];
            
            // Check if we got a valid response structure
            if (lastResponse.choices && lastResponse.choices[0] && lastResponse.choices[0].message) {
                results.push('✅ Valid OpenAI API response structure');
                
                const assistantMessage = lastResponse.choices[0].message.content.toLowerCase();
                
                // Check for expected answer to "what day comes after monday?"
                if (message.includes('monday') && message.includes('after')) {
                    if (assistantMessage.includes('tuesday')) {
                        results.push('✅ Correct answer: Response mentions Tuesday');
                    } else {
                        results.push('⚠️ Unexpected answer: Response does not mention Tuesday');
                    }
                }
                
                // Check token exchange success
                results.push('✅ Cross-client token exchange successful');
                results.push('✅ API authentication with exchanged token worked');
                
            } else {
                results.push('❌ Invalid response structure');
            }

            testResultsDiv.innerHTML = '<strong>Test Results:</strong><br>' + results.join('<br>');
            testResultsDiv.className = 'status success';
            testResultsDiv.style.display = 'block';
        }

        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            statusDiv.style.display = 'block';
        }

        function logResponse(title, data) {
            const responseDiv = document.getElementById('response');
            const timestamp = new Date().toLocaleTimeString();
            const content = typeof data === 'string' ? data : JSON.stringify(data, null, 2);
            responseDiv.textContent += `[${timestamp}] ${title}\n${content}\n\n`;
            responseDiv.style.display = 'block';
            responseDiv.scrollTop = responseDiv.scrollHeight;
        }

        function clearResponse() {
            document.getElementById('response').textContent = '';
            document.getElementById('response').style.display = 'none';
            document.getElementById('status').style.display = 'none';
            document.getElementById('testResults').style.display = 'none';
            lastResponse = null;
        }

        function goHome() {
            window.location.href = 'index.html';
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', () => {
            refreshTokenInfo();
            
            // Check if we have a token
            const token = localStorage.getItem('access_token');
            if (!token) {
                showStatus('❌ No authentication token found. Please go back and authenticate first.', 'error');
            }
        });
    </script>
</body>
</html>
