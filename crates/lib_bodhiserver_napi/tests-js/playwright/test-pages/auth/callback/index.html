<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OAuth Callback - Processing</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
            margin: 20px auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 4px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .status.info {
            background-color: #e7f3ff;
            color: #004085;
        }
        .details {
            text-align: left;
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>OAuth Callback Processing</h1>
        
        <div id="loading" style="display: block;">
            <div class="spinner"></div>
            <p>Processing OAuth callback...</p>
        </div>

        <div id="status" class="status" style="display: none;"></div>
        <div id="details" class="details" style="display: none;"></div>
        
        <div id="actions" style="display: none;">
            <button onclick="goToChat()">Continue to Chat Test</button>
            <button onclick="goHome()" style="background-color: #6c757d;">Back to Home</button>
        </div>
    </div>

    <script>
        // OAuth configuration
        const CONFIG = {
            authUrl: 'https://dev-id.getbodhi.app',
            realm: 'bodhi',
            clientId: 'test-resource-059a5742-2d7c-44cd-b2fa-2952e8fabee3', // Third-party client
            clientSecret: '2BibHqdcmgEks2LCxXqfrzO4rsq80XX4', // Third-party secret
            redirectUri: window.location.origin + '/auth/callback/'
        };

        async function processCallback() {
            try {
                // Parse URL parameters
                const urlParams = new URLSearchParams(window.location.search);
                const code = urlParams.get('code');
                const state = urlParams.get('state');
                const error = urlParams.get('error');

                logDetails('URL Parameters:', { code: code ? 'present' : 'missing', state, error });

                if (error) {
                    throw new Error(`OAuth error: ${error} - ${urlParams.get('error_description') || 'Unknown error'}`);
                }

                if (!code) {
                    throw new Error('No authorization code received');
                }

                // Verify state parameter
                const storedState = localStorage.getItem('oauth_state');
                if (!storedState || storedState !== state) {
                    throw new Error('Invalid state parameter - possible CSRF attack');
                }

                logDetails('State verification:', { stored: storedState, received: state, valid: storedState === state });

                // Get stored PKCE parameters
                const codeVerifier = localStorage.getItem('oauth_code_verifier');
                if (!codeVerifier) {
                    throw new Error('No code verifier found in storage');
                }

                showStatus('Exchanging authorization code for token...', 'info');

                // Exchange code for token
                const tokenResponse = await exchangeCodeForToken(code, codeVerifier);
                logDetails('Token exchange response:', tokenResponse);

                if (tokenResponse.access_token) {
                    // Store the token
                    localStorage.setItem('access_token', tokenResponse.access_token);
                    if (tokenResponse.refresh_token) {
                        localStorage.setItem('refresh_token', tokenResponse.refresh_token);
                    }

                    // Parse token to show info
                    const payload = JSON.parse(atob(tokenResponse.access_token.split('.')[1]));
                    logDetails('Token payload:', payload);

                    showStatus('✅ Token received successfully! This token is from the third-party client and will be exchanged by our server.', 'success');
                    
                    // Clean up OAuth state
                    localStorage.removeItem('oauth_state');
                    localStorage.removeItem('oauth_code_verifier');
                    localStorage.removeItem('oauth_timestamp');

                    // Show actions
                    document.getElementById('actions').style.display = 'block';

                } else {
                    throw new Error('No access token in response');
                }

            } catch (error) {
                console.error('Callback processing error:', error);
                showStatus('❌ Error: ' + error.message, 'error');
                logDetails('Error details:', error);
                
                // Show actions even on error
                document.getElementById('actions').style.display = 'block';
            } finally {
                document.getElementById('loading').style.display = 'none';
            }
        }

        async function exchangeCodeForToken(code, codeVerifier) {
            const tokenUrl = `${CONFIG.authUrl}/realms/${CONFIG.realm}/protocol/openid-connect/token`;
            
            const params = new URLSearchParams({
                grant_type: 'authorization_code',
                client_id: CONFIG.clientId,
                client_secret: CONFIG.clientSecret,
                code: code,
                redirect_uri: CONFIG.redirectUri,
                code_verifier: codeVerifier
            });

            logDetails('Token request:', {
                url: tokenUrl,
                params: Object.fromEntries(params.entries())
            });

            const response = await fetch(tokenUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: params
            });

            const responseText = await response.text();
            logDetails('Raw token response:', { status: response.status, body: responseText });

            if (!response.ok) {
                throw new Error(`Token exchange failed: ${response.status} - ${responseText}`);
            }

            return JSON.parse(responseText);
        }

        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = message;
            statusDiv.className = `status ${type}`;
            statusDiv.style.display = 'block';
        }

        function logDetails(title, data) {
            const detailsDiv = document.getElementById('details');
            const timestamp = new Date().toLocaleTimeString();
            detailsDiv.innerHTML += `<strong>[${timestamp}] ${title}</strong>\n${JSON.stringify(data, null, 2)}\n\n`;
            detailsDiv.style.display = 'block';
            detailsDiv.scrollTop = detailsDiv.scrollHeight;
        }

        function goToChat() {
            window.location.href = '../../chat.html';
        }

        function goHome() {
            window.location.href = '../../index.html';
        }

        // Start processing when page loads
        document.addEventListener('DOMContentLoaded', processCallback);
    </script>
</body>
</html>
