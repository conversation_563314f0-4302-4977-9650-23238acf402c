{"name": "@bodhiapp/app-bindings", "version": "0.0.12-dev", "description": "NAPI-RS bindings for BodhiApp server with integrated tests", "main": "index.js", "types": "index.d.ts", "napi": {"name": "app-bindings", "triples": {"defaults": false, "additional": ["aarch64-apple-darwin", "x86_64-pc-windows-msvc", "x86_64-unknown-linux-gnu"]}}, "scripts": {"artifacts": "napi artifacts", "create-npm-dirs": "napi create-npm-dir --target .", "build": "napi build --platform --release", "build:debug": "napi build --platform", "build:release": "napi build --platform --release", "build:release:win": "napi build --platform --release --target x86_64-pc-windows-msvc", "update-optional-dependencies": "node scripts/update-optional-dependencies.js", "verify-packages": "node scripts/verify-npm-packages.js", "test": "npm run test:run && npm run test:playwright", "test:run": "vitest run", "test:playwright": "playwright test --config=playwright.config.mjs --reporter=list", "test:playwright:ci": "playwright test --config=playwright.config.mjs", "test:playwright:ui": "playwright test --config=playwright.config.mjs --ui", "test:playwright:headed": "playwright test --config=playwright.config.mjs --headed", "test:all": "npm run test:run && npm run test:playwright", "format": "biome format --write .", "format:check": "biome format .", "lint": "biome lint .", "lint:fix": "biome lint --write .", "check": "biome check .", "check:fix": "biome check --write ."}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@napi-rs/cli": "^2.18.4", "@playwright/test": "^1.48.2", "dotenv": "^16.5.0", "vitest": "^2.1.8"}, "engines": {"node": ">=22"}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "repository": {"type": "git", "url": "git+https://github.com/BodhiSearch/BodhiApp.git", "directory": "crates/lib_bodhiserver_napi"}, "homepage": "https://github.com/BodhiSearch/BodhiApp#readme", "bugs": {"url": "https://github.com/BodhiSearch/BodhiApp/issues"}, "keywords": ["napi", "rust", "node-addon", "bodhi", "llm", "server"], "files": ["index.d.ts", "index.js"], "optionalDependencies": {"@bodhiapp/app-bindings-darwin-arm64": "0.0.11", "@bodhiapp/app-bindings-linux-x64-gnu": "0.0.11", "@bodhiapp/app-bindings-win32-x64-msvc": "0.0.11"}}